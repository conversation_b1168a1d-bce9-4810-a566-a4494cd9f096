package service

import (
	"encoding/json"
	"fmt"
	"strconv"
	remote_control_request "wukong-api/cloud_sdk/cloud_api/remote_control/request"
	"wukong-api/internal/dto"
	"wukong-api/internal/model"

	"github.com/go-redis/redis"

	"math/rand"
	"sync"
	"time"
	"wukong-api/internal/fileds"
	"wukong-api/internal/repo"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type WebSocketService struct {
	db                 *gorm.DB
	logger             *zap.SugaredLogger
	wsManager          *repo.WsManager
	redis              *redis.Client
	deviceRedisService *DeviceRedisService
	droneService       *DroneService
}

func NewWebSocketService() *WebSocketService {
	return &WebSocketService{
		db:                 repo.GetDatabase(),
		logger:             repo.GetLogger(),
		wsManager:          repo.NewWsManager(),
		redis:              repo.GetRedis(0),
		deviceRedisService: NewDeviceRedisService(),
		droneService:       NewDroneService(),
	}
}

// DispatchSub 分发业务处理
func (c *WebSocketService) DispatchSub(id string, serviceKey string, reqData json.RawMessage, extInfo dto.WSExtInfo) error {
	// 获取当前用户的websocket连接会话
	conn := c.wsManager.WsConn[id]
	// 根据业务类型，路由到对应的业务处理
	switch serviceKey {
	case fileds.DroneDetail:
		var droneDetailData dto.ReqDroneDetail
		err := json.Unmarshal(reqData, &droneDetailData)
		if err != nil {
			c.logger.Errorf("转换为请求参数错误:droneDetailData")
			return err
		}

		isExistBid := c.registerWsManager(id, conn, droneDetailData.VehicleId, serviceKey)
		// 已添加的 VehicleId（无人机sn）就不需要再开启协程 ，一个 VehicleId（无人机sn）对应一个协程
		if !isExistBid {
			go c.droneDetailHandler(droneDetailData)
		}

	case fileds.LiveReadyNotice:
		var liveReadyNotice dto.ReqLiveReadyNotice
		err := json.Unmarshal(reqData, &liveReadyNotice)
		if err != nil {
			c.logger.Errorf("转换为请求参数错误:liveReadyNotice")
			return err
		}

		c.registerWsManager(id, conn, liveReadyNotice.VehicleId, serviceKey)
		c.liveReadyNoticeHandler(liveReadyNotice)

	case fileds.DroneMgrInfo:
		// 不需要特定的 bid，统一约定为 serviceKey
		isExistBid := c.registerWsManager(id, conn, fileds.DroneMgrInfo, serviceKey)
		if !isExistBid {
			go c.droneMgrInfoHandler(extInfo.CID)
		}

	case fileds.ObjectSignUrl:
		var reqObjectSignUrlData dto.ReqObjectSignUrl
		err := json.Unmarshal(reqData, &reqObjectSignUrlData)
		if err != nil {
			c.logger.Errorf("转换为请求参数错误:objectSignUrl")
			return err
		}

		c.registerWsManager(id, conn, reqObjectSignUrlData.Path, serviceKey)

		// 保存私有资源的状态
		objectKeyExpireMap := make(map[string]int64)
		for _, objectKey := range reqObjectSignUrlData.ObjectKey {
			objectKeyExpireMap[objectKey] = time.Now().Unix()
		}

		repo.OssPrivateObjManagerLock.Lock()

		if _, ok := c.wsManager.OssPrivateObjManager[id]; ok {
			c.wsManager.OssPrivateObjManager[id][reqObjectSignUrlData.Path] = objectKeyExpireMap
		} else {
			bidMap := make(map[string]map[string]int64)
			bidMap[reqObjectSignUrlData.Path] = objectKeyExpireMap
			c.wsManager.OssPrivateObjManager[id] = bidMap
		}

		repo.OssPrivateObjManagerLock.Unlock()

		// 开启协程轮询检查
		go c.objectSignUrlHandler(id, reqObjectSignUrlData.Path)

	case fileds.SandboxDeviceOverview:
		isExistBid := c.registerWsManager(id, conn, strconv.Itoa(extInfo.CID), serviceKey)
		if !isExistBid {
			go c.sandboxDeviceOverviewHandler(extInfo.CID)
		}

	case fileds.SandboxTrajectoryOverview:
		isExistBid := c.registerWsManager(id, conn, strconv.Itoa(extInfo.CID), serviceKey)
		if !isExistBid {
			go c.sandboxTrajectoryOverviewHandler(extInfo.CID)
		}

	case fileds.SandboxWarnRecordLatest:
		isExistBid := c.registerWsManager(id, conn, strconv.Itoa(extInfo.CID), serviceKey)
		if !isExistBid {
			go c.sandboxWarnRecordLatestHandler(extInfo.CID)
		}

	case fileds.SandboxFlightDeviceInfo:
		var droneSnReq dto.DroneSnReq
		err := json.Unmarshal(reqData, &droneSnReq)
		if err != nil {
			c.logger.Errorf("DispatchSub: 获取请求参数错误. service_key:%s.", serviceKey)
			return err
		}

		isExistBid := c.registerWsManager(id, conn, droneSnReq.DroneSn, serviceKey)
		if !isExistBid {
			go c.sandboxFlightDeviceInfoHandler(droneSnReq.DroneSn)
		}

	case fileds.CockpitData:
		var droneSnReq dto.DroneSnReq
		err := json.Unmarshal(reqData, &droneSnReq)
		if err != nil {
			c.logger.Errorf("DispatchSub: 获取为请求参数错误. service_key:%s.", serviceKey)
			return err
		}

		isExistBid := c.registerWsManager(id, conn, droneSnReq.DroneSn, serviceKey)
		if !isExistBid {
			go c.cockpitDataHandler(droneSnReq.DroneSn)
		}

	case fileds.CockpitDeviceFlightInfo:
		var droneSnReq dto.DroneSnReq
		err := json.Unmarshal(reqData, &droneSnReq)
		if err != nil {
			c.logger.Errorf("DispatchSub: 获取为请求参数错误. service_key:%s.", serviceKey)
			return err
		}

		isExistBid := c.registerWsManager(id, conn, droneSnReq.DroneSn, serviceKey)
		if !isExistBid {
			go c.cockpitDeviceFlightInfoHandler(droneSnReq.DroneSn)
		}

	case fileds.TunnelData:
		var pathComm dto.PathComm
		err := json.Unmarshal(reqData, &pathComm)
		if err != nil {
			c.logger.Errorf("DispatchSub: 转换为请求参数错误. service_key:%s.", serviceKey)
			return err
		}

		isExistBid := c.registerWsManager(id, conn, fileds.TunnelData, serviceKey)
		if !isExistBid {
			go c.tunnelInfoHandler(pathComm)
		}

	case fileds.TunnelFourWayData:
		var pathComm dto.PathComm
		err := json.Unmarshal(reqData, &pathComm)
		if err != nil {
			c.logger.Errorf("DispatchSub: 转换为请求参数错误. service_key:%s.", serviceKey)
			return err
		}

		isExistBid := c.registerWsManager(id, conn, fileds.TunnelFourWayData, serviceKey)
		if !isExistBid {
			go c.tunnelFourWayDataHandler(pathComm)
		}

	case fileds.SpeakerStatus:
		var data dto.SpeakerStatusReq
		err := json.Unmarshal(reqData, &data)
		if err != nil {
			return err
		}

		isExistBid := c.registerWsManager(id, conn, data.DroneSN, serviceKey)
		if !isExistBid {
			go c.SpeakerStatusHandler(data.DroneSN)
		}

	case fileds.LightStatus:
		var data dto.LightStatusReq
		err := json.Unmarshal(reqData, &data)
		if err != nil {
			return err
		}

		isExistBid := c.registerWsManager(id, conn, data.DroneSN, serviceKey)
		if !isExistBid {
			go c.LightStatusHandler(data.DroneSN)
		}
	case fileds.RealTimeAlarmInfo:
		var data dto.RealTimeAlarmInfoReq
		err := json.Unmarshal(reqData, &data)
		if err != nil {
			return err
		}
		c.registerWsManager(id, conn, data.DroneSN, serviceKey)
	case fileds.AutonomousFlyingAround:
		var data dto.AutonomousFlyingAroundReq
		err := json.Unmarshal(reqData, &data)
		if err != nil {
			return err
		}
		c.registerWsManager(id, conn, data.DroneSN, serviceKey)
	}

	return nil
}

// registerWsManager 将用户注册到wsManager中
func (c *WebSocketService) registerWsManager(id string, conn *repo.WsConn, bid string, serviceKey string) (isExistBid bool) {
	repo.ServiceSessionsMapLock.Lock()

	// 判断该bid是否存在。已存在该bidMap就直接存入，否则存入新注册的bidMap
	bidFlag := false
	for i := range c.wsManager.ServiceSessionsMap[serviceKey] {
		if i == bid {
			bidFlag = true
			c.wsManager.ServiceSessionsMap[serviceKey][bid][id] = conn
		}
	}
	if !bidFlag {
		wsConnMap := make(repo.WsConnMap)
		wsConnMap[id] = conn
		// 存入新注册的bidMap
		if c.wsManager.ServiceSessionsMap[serviceKey] == nil {
			bidMap := make(map[string]repo.WsConnMap)
			bidMap[bid] = wsConnMap
			c.wsManager.ServiceSessionsMap[serviceKey] = bidMap
		} else {
			c.wsManager.ServiceSessionsMap[serviceKey][bid] = wsConnMap
		}
	}

	repo.ServiceSessionsMapLock.Unlock()

	return bidFlag
}

// droneDetailHandle 处理获取无人机基础信息业务
func (c *WebSocketService) droneDetailHandler(droneDetailData dto.ReqDroneDetail) {
	ticker := time.NewTicker(5 * time.Second)
	for {
		// 定时推送无人机基础信息消息；如果bid（无人机sn）不存在，则说明没有用户订阅获取该无人机基础信息功能，结束协程处理
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[fileds.DroneDetail] != nil && c.wsManager.ServiceSessionsMap[fileds.DroneDetail][droneDetailData.VehicleId] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			c.logger.Debugf("droneDetailHandler exit: %v", droneDetailData.VehicleId)
			c.logger.Debugf("droneDetailHandler ServiceSessionsMap: %v", c.wsManager.ServiceSessionsMap)
			return
		}

		c.logger.Debugf("droneDetailHandler doing: %v", droneDetailData.VehicleId)

		// 判断无人机是否在线
		isOnline, err := c.deviceRedisService.CheckDeviceOnline(droneDetailData.VehicleId)
		if err != nil {
			return
		}

		if isOnline {
			// 获取 redis 中的无人机基础信息，获取到了就取出推送 websocket 消息
			baseKey := fmt.Sprintf(fileds.DeviceBasePrefix, droneDetailData.VehicleId)
			deviceBaseData, err := c.redis.Get(baseKey).Result()
			if err != nil {
				c.logger.Errorf("获取无人机基础信息异常:%v, err:%s", droneDetailData.VehicleId, err.Error())
				break
			}
			c.wsManager.BroadCastData([]byte(deviceBaseData), fileds.DroneDetail, droneDetailData.VehicleId, "")
		} else {
			var drone *model.Drone
			drone, err = c.droneService.GetDroneByVehicleID(droneDetailData.VehicleId)
			if err != nil {
				return
			}

			data := make(map[string]int)
			data[fileds.DroneDetail] = drone.WorkStatus
			encodedData, err := json.Marshal(data)
			if err != nil {
				c.logger.Errorf("fail to encode workStatus:%v, err:%s", droneDetailData.VehicleId, err.Error())
				break
			}
			c.wsManager.BroadCastData(encodedData, fileds.DroneDetail, droneDetailData.VehicleId, "")
		}

		<-ticker.C
	}
}

// liveReadyNoticeHandle 处理获取无人机直播ready通知业务
func (c *WebSocketService) liveReadyNoticeHandler(notice dto.ReqLiveReadyNotice) {
	// 检查redis有没有存，存了就直接取出推送，没存就不管
	key := fmt.Sprintf(fileds.LiveReadyNoticePrefix, notice.VehicleId)
	result, err := c.redis.Get(key).Result()
	if err != nil || result == "" {
		c.logger.Infof("暂时没有获得无人机直播ready通知，key:%v", key)
		return
	}
	c.wsManager.BroadCastData([]byte(result), fileds.LiveReadyNotice, notice.VehicleId, "")
}

// droneMgrInfoHandle 处理获取droneMgrInfo业务
func (c *WebSocketService) droneMgrInfoHandler(cid int) {
	ticker := time.NewTicker(10 * time.Second)
	for {
		// 定时推送droneMgrInfo；如果bid不存在，则说明没有用户订阅获取该业务功能，结束协程处理
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[fileds.DroneMgrInfo] != nil && c.wsManager.ServiceSessionsMap[fileds.DroneMgrInfo][fileds.DroneMgrInfo] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			return
		}

		droneMgrInfo, err := c.redis.Get(fileds.WSDroneMgrInfoPrefix).Result()
		if err != nil {
			if err == redis.Nil {
				mgrInfo, err := NewDroneService().GetMgrInfo(cid)
				if err != nil {
					c.logger.Errorf("查询droneMgrInfo出错:%s", err.Error())
					break
				}

				resp := make(map[string]*MgrInfoResp)
				resp[fileds.DroneMgrInfo] = mgrInfo

				encodedData, err := json.Marshal(resp)
				if err != nil {
					c.logger.Errorf("fail to encode droneMgrInfo, err:%s", err.Error())
					break
				}
				c.wsManager.BroadCastData(encodedData, fileds.DroneMgrInfo, fileds.DroneMgrInfo, "")

				err = c.redis.Set(fileds.WSDroneMgrInfoPrefix, encodedData, time.Duration(5)*time.Second).Err()
				if err != nil {
					c.logger.Errorf("set key:%s failed %s", fileds.WSDroneMgrInfoPrefix, err.Error())
				}
			} else {
				c.logger.Errorf("fail to get droneMgrInfo, err:%s", err.Error())
			}
		} else {
			c.wsManager.BroadCastData([]byte(droneMgrInfo), fileds.DroneMgrInfo, fileds.DroneMgrInfo, "")
		}

		<-ticker.C
	}
}

// objectSignUrlHandler 处理获取oss资源签名地址业务
func (c *WebSocketService) objectSignUrlHandler(id string, bid string) {
	ticker := time.NewTicker(2 * time.Minute)
	for {
		// 定时轮询检查objectKey是否过期；如果bid.id不存在，则说明用户已经取消订阅获取该业务功能，结束协程处理
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[fileds.ObjectSignUrl] != nil &&
			c.wsManager.ServiceSessionsMap[fileds.ObjectSignUrl][bid] != nil &&
			c.wsManager.ServiceSessionsMap[fileds.ObjectSignUrl][bid][id] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			return
		}

		if err := c.CheckObjectKeyExpire(id, bid); err != nil {
			c.logger.Errorf("objectSignUrlHandler.checkObjectKeyExpire() err:%s", err.Error())
		}

		<-ticker.C
	}
}

// CheckObjectKeyExpire 检查objectKey是否过期
func (c *WebSocketService) CheckObjectKeyExpire(id string, bid string) error {
	repo.OssPrivateObjManagerLock.Lock()
	defer repo.OssPrivateObjManagerLock.Unlock()

	// 遍历bidMap，判断对应的objectKey是否到期，是的话就将objectKey放到list中，进行下一步处理
	if idMap, ok := c.wsManager.OssPrivateObjManager[id]; ok {
		if bidMap, ok := idMap[bid]; ok {
			var expireObjectKeyList []string
			for objectKey, expire := range bidMap {
				if expire <= time.Now().Unix() {
					expireObjectKeyList = append(expireObjectKeyList, objectKey)
				}
			}

			if len(expireObjectKeyList) > 0 {
				if err := c.handleExpireObjectKey(id, bid, expireObjectKeyList); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// handleExpireObjectKey 处理到期的的objectKey，生成签名地址，推送消息，更新过期时间
func (c *WebSocketService) handleExpireObjectKey(id string, bid string, expireObjectKeyList []string) error {
	ossService := NewOssService()

	urlSyncMap := sync.Map{}
	expireSyncMap := sync.Map{}
	var wg sync.WaitGroup

	for _, objectKey := range expireObjectKeyList {
		// 并行生成签名地址
		wg.Add(1)
		go func(key string) {
			defer wg.Done()

			// 随机生成expire，30-60 min
			expire := (rand.Intn(31) + 30) * 60
			url, err := ossService.GetObjectSignURL(key, int64(expire))
			if err != nil {
				c.logger.Errorf("fail to get object sign url, err:%s", err.Error())
				return
			}

			// 将expire计算成新的过期时间戳，取expire的三分之二，提前在过期时间到之前更新的过期时间
			advanceTime := (expire * 2) / 3
			newTime := time.Now().Add(time.Second * time.Duration(advanceTime))
			timestamp := newTime.Unix()

			// 将url，timestamp分别放到map里
			urlSyncMap.Store(key, url)
			expireSyncMap.Store(key, timestamp)
		}(objectKey)
	}

	wg.Wait()

	// 遍历并发map，封装返回数据，通过 websocket 指定 bid.id 推送文件签名地址；更新全局变量的expire
	urlMap := make(map[string]string)
	urlSyncMap.Range(func(key, value interface{}) bool {
		urlMap[key.(string)] = value.(string)
		return true
	})

	data := make(map[string]map[string]string)
	data[fileds.ObjectSignUrl] = urlMap
	encodedData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	c.wsManager.BroadCastData(encodedData, fileds.ObjectSignUrl, bid, id)

	// 更新全局变量的expire
	repo.OssPrivateObjManagerLock.Lock()
	if idMap, ok := c.wsManager.OssPrivateObjManager[id]; ok {
		if bidMap, ok := idMap[bid]; ok {
			expireSyncMap.Range(func(key, value interface{}) bool {
				bidMap[key.(string)] = value.(int64)
				return true
			})
		}
	}
	repo.OssPrivateObjManagerLock.Unlock()

	return nil
}

// sandboxDeviceOverviewHandler 处理获取沙盘设备概览业务
func (c *WebSocketService) sandboxDeviceOverviewHandler(cid int) {
	ticker := time.NewTicker(2 * time.Second)

	bid := strconv.Itoa(cid)
	serviceKey := fileds.SandboxDeviceOverview
	cockpitSvc := NewCockpitFlightService()

	for {
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[serviceKey] != nil && c.wsManager.ServiceSessionsMap[serviceKey][bid] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			return
		}

		data, err := cockpitSvc.DeviceOverview(cid)
		if err != nil {
			c.logger.Errorf("sandboxDeviceOverviewHandler: %v", err)
			continue
		}

		resp := make(map[string]*dto.DeviceOverviewResp)
		resp[serviceKey] = data

		encodedData, err := json.Marshal(resp)
		if err != nil {
			c.logger.Errorf("sandboxDeviceOverviewHandler: failed to encode DeviceOverviewResp, err:%s", err.Error())
			continue
		}

		c.wsManager.BroadCastData(encodedData, serviceKey, bid, "")
		<-ticker.C
	}
}

// sandboxTrajectoryOverviewHandler 处理获取沙盘轨迹概览业务
func (c *WebSocketService) sandboxTrajectoryOverviewHandler(cid int) {
	ticker := time.NewTicker(2 * time.Second)

	bid := strconv.Itoa(cid)
	serviceKey := fileds.SandboxTrajectoryOverview
	cockpitSvc := NewCockpitFlightService()

	for {
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[serviceKey] != nil && c.wsManager.ServiceSessionsMap[serviceKey][bid] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			return
		}

		data, err := cockpitSvc.TrajectoryOverview(cid)
		if err != nil {
			c.logger.Errorf("sandboxTrajectoryOverviewHandler: %v", err)
			continue
		}

		resp := make(map[string]*dto.TrajectoryOverviewResp)
		resp[serviceKey] = data

		encodedData, err := json.Marshal(resp)
		if err != nil {
			c.logger.Errorf("sandboxDeviceOverviewHandler: failed to encode TrajectoryOverviewResp, err:%s", err.Error())
			continue
		}

		c.wsManager.BroadCastData(encodedData, serviceKey, bid, "")
		<-ticker.C
	}
}

// sandboxWarnRecordLatestHandler 处理获取沙盘最新告警记录业务
func (c *WebSocketService) sandboxWarnRecordLatestHandler(cid int) {
	ticker := time.NewTicker(2 * time.Second)

	bid := strconv.Itoa(cid)
	serviceKey := fileds.SandboxWarnRecordLatest
	cockpitSvc := NewCockpitFlightService()

	for {
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[serviceKey] != nil && c.wsManager.ServiceSessionsMap[serviceKey][bid] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			return
		}

		params := dto.JobWarnRecordListReq{
			Offset: 0,
			Limit:  30,
			Status: 2,
		}
		data, err := cockpitSvc.LatestWarnRecord(params, cid)
		if err != nil {
			c.logger.Errorf("sandboxWarnRecordLatestHandler: %v", err)
			continue
		}

		resp := make(map[string][]dto.JobWarnRecordResp)
		resp[serviceKey] = data

		encodedData, err := json.Marshal(resp)
		if err != nil {
			c.logger.Errorf("sandboxWarnRecordLatestHandler: failed to encode JobWarnRecordResp, err:%s", err.Error())
			continue
		}

		c.wsManager.BroadCastData(encodedData, serviceKey, bid, "")
		<-ticker.C
	}
}

// sandboxFlightDeviceInfoHandler 处理获取沙盘飞行设备信息业务
func (c *WebSocketService) sandboxFlightDeviceInfoHandler(droneSn string) {
	ticker := time.NewTicker(2 * time.Second)

	bid := droneSn
	serviceKey := fileds.SandboxFlightDeviceInfo
	cockpitSvc := NewCockpitFlightService()

	for {
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[serviceKey] != nil && c.wsManager.ServiceSessionsMap[serviceKey][bid] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			return
		}

		data, err := cockpitSvc.FlightDeviceInfo(droneSn)
		if err != nil {
			c.logger.Errorf("sandboxFlightDeviceInfoHandler: %v", err)
			continue
		}

		resp := make(map[string]*dto.FlightDeviceInfoResp)
		resp[serviceKey] = data

		encodedData, err := json.Marshal(resp)
		if err != nil {
			c.logger.Errorf("sandboxFlightDeviceInfoHandler: failed to encode FlightDeviceInfoResp, err:%s", err.Error())
			continue
		}

		c.wsManager.BroadCastData(encodedData, serviceKey, bid, "")
		<-ticker.C
	}
}

// cockpitDataHandler 处理获取驾驶舱数据业务
func (c *WebSocketService) cockpitDataHandler(droneSn string) {
	ticker := time.NewTicker(1 * time.Second)

	bid := droneSn
	serviceKey := fileds.CockpitData
	cockpitSvc := NewCockpitFlightService()

	for {
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[serviceKey] != nil && c.wsManager.ServiceSessionsMap[serviceKey][bid] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			return
		}

		deviceInfo, err := cockpitSvc.FlightDeviceInfo(droneSn)
		if err != nil {
			c.logger.Errorf("cockpitDataHandler: %v", err)
			continue
		}

		monitor, err := cockpitSvc.TaskMonitor(deviceInfo.Dock.DeviceSn, droneSn)
		if err != nil {
			c.logger.Errorf("cockpitDataHandler: %v", err)
			continue
		}

		data := map[string]interface{}{
			"device_info": deviceInfo,
			"monitor":     monitor,
		}

		resp := make(map[string]map[string]interface{})
		resp[serviceKey] = data

		encodedData, err := json.Marshal(resp)
		if err != nil {
			c.logger.Errorf("cockpitDataHandler: failed to encode resp, err:%s", err.Error())
			continue
		}

		c.wsManager.BroadCastData(encodedData, serviceKey, bid, "")
		<-ticker.C
	}
}

// cockpitDeviceFlightInfoHandler 处理获取驾驶舱设备飞行信息业务
func (c *WebSocketService) cockpitDeviceFlightInfoHandler(droneSn string) {
	ticker := time.NewTicker(1 * time.Second)

	bid := droneSn
	serviceKey := fileds.CockpitDeviceFlightInfo
	cockpitSvc := NewCockpitFlightService()

	for {
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[serviceKey] != nil && c.wsManager.ServiceSessionsMap[serviceKey][bid] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			return
		}

		data, err := cockpitSvc.DroneFlightInfo(droneSn)
		if err != nil {
			c.logger.Errorf("cockpitDeviceFlightInfoHandler: %v", err)
			continue
		}

		resp := make(map[string]*dto.DroneFlightInfoResp)
		resp[serviceKey] = data

		encodedData, err := json.Marshal(resp)
		if err != nil {
			c.logger.Errorf("cockpitDeviceFlightInfoHandler: failed to encode DroneFlightInfoResp, err:%s", err.Error())
			continue
		}

		c.wsManager.BroadCastData(encodedData, serviceKey, bid, "")
		<-ticker.C
	}
}

// SpeakerStatusHandler 处理获取喊话器状态业务
func (c *WebSocketService) SpeakerStatusHandler(droneSn string) {
	ticker := time.NewTicker(1 * time.Second)

	bid := droneSn
	serviceKey := fileds.SpeakerStatus
	payloadSvc := NewPayloadService()

	for {
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[serviceKey] != nil && c.wsManager.ServiceSessionsMap[serviceKey][bid] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			return
		}

		data, err := payloadSvc.GetSpeakerStatus(droneSn)
		if err != nil {
			c.logger.Errorf("SpeakerStatusHandler: %v", err)
			continue
		}

		if data != nil {
			resp := make(map[string]map[string]interface{})
			resp[serviceKey] = data

			var encodedData []byte
			encodedData, err = json.Marshal(resp)
			if err != nil {
				c.logger.Errorf("SpeakerStatusHandler: failed to encode resp, err:%s", err.Error())
				continue
			}

			c.wsManager.BroadCastData(encodedData, serviceKey, bid, "")
		}

		<-ticker.C
	}
}

// LightStatusHandler 处理获取探照灯状态业务
func (c *WebSocketService) LightStatusHandler(droneSn string) {
	ticker := time.NewTicker(1 * time.Second)

	bid := droneSn
	serviceKey := fileds.LightStatus
	payloadSvc := NewPayloadService()

	for {
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[serviceKey] != nil && c.wsManager.ServiceSessionsMap[serviceKey][bid] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			return
		}

		data, err := payloadSvc.GetLightStatus(droneSn)
		if err != nil {
			c.logger.Errorf("LightStatusHandler: %v", err)
			continue
		}

		if data != nil {
			resp := make(map[string]*remote_control_request.DrcPsdkStateInfo)
			resp[serviceKey] = data

			var encodedData []byte
			encodedData, err = json.Marshal(resp)
			if err != nil {
				c.logger.Errorf("LightStatusHandler: failed to encode resp, err:%s", err.Error())
				continue
			}

			c.wsManager.BroadCastData(encodedData, serviceKey, bid, "")
		}

		<-ticker.C
	}
}

func (c *WebSocketService) DispatchReceivedData(connID string, message []byte) {
	resp := &dto.MsgResp{}
	if err := json.Unmarshal(message, &resp); err != nil {
		c.logger.Errorf("failed to unmarshal message, connID:%s, err:%s", connID, err.Error())
		return
	}

	if !resp.Succeeded {
		c.logger.Errorf("received message, response is error, connID:%s, err:%s", connID, resp.Msg)
	}

	switch resp.Type {
	case fileds.NoRealTimeVideoResp:
		data := dto.NoRealTimeVideoResp{}
		if err := json.Unmarshal(resp.Data, &data); err != nil {
			c.logger.Errorf("failed to unmarshal message, connID:%s, err:%s", connID, err.Error())
			return
		}

		go NewJobDataProcessorService().HandleNoRealTimeVideoDataResp(resp.Succeeded, data)

	case fileds.NoRealTimePicResp:
		data := dto.NoRealTimePicResp{}
		if err := json.Unmarshal(resp.Data, &data); err != nil {
			c.logger.Errorf("failed to unmarshal message, connID:%s, err:%s", connID, err.Error())
			return
		}

		go NewJobDataProcessorService().HandleNoRealTimePicDataResp(resp.Succeeded, data)

	case fileds.RealTimeResp:
		data := dto.RealTimeResp{}
		if err := json.Unmarshal(resp.Data, &data); err != nil {
			c.logger.Errorf("failed to unmarshal message, connID:%s, err:%s", connID, err.Error())
			return
		}

		go NewJobDataProcessorService().HandleRealTimeDataResp(resp.Succeeded, data)

	case fileds.RealTimeControlResp:
		data := dto.RealTimeControlResp{}
		if err := json.Unmarshal(resp.Data, &data); err != nil {
			c.logger.Errorf("failed to unmarshal message, connID:%s, err:%s", connID, err.Error())
			return
		}

		go NewJobDataProcessorService().handleRealTimeControlResp(resp.Succeeded, data)

	}
}

// tunnelInfoHandler 处理隧道信息请求
func (c *WebSocketService) tunnelInfoHandler(pathComm dto.PathComm) {
	// 模拟根据经纬度查询隧道信息
	// 这里应该是查询数据库或其他数据源的逻辑
	hasTunnel := true // 模拟查询结果，实际应该根据真实查询结果设置

	var response interface{}
	if hasTunnel {
		// 有隧道时返回隧道信息
		response = dto.TunnelData{
			TunnelName:       "示例隧道",
			Depth:            15.5,
			TunnelType:       "地铁隧道",
			ConstructionDate: "2020-01-01",
			Diameter:         6.5,
			Material:         "钢筋混凝土",
			AdditionalInfo: map[string]any{
				"状态":     "正常",
				"上次检查时间": "2024-01-15",
			},
		}
	} else {
		// 没有隧道时返回空
		response = dto.TunnelData{
			TunnelName:       "",
			Depth:            0,
			TunnelType:       "",
			ConstructionDate: "",
			Diameter:         0,
			Material:         "",
			AdditionalInfo:   nil,
		}
	}

	// 将数据编码为JSON
	data := make(map[string]interface{})
	data[fileds.TunnelData] = response
	encodedData, err := json.Marshal(data)
	if err != nil {
		c.logger.Errorf("tunnelInfoHandler: marshal tunnel data failed: %v", err)
		return
	}

	// 广播数据给订阅者
	c.wsManager.BroadCastData(encodedData, fileds.TunnelData, fileds.TunnelData, "")
}

// tunnelFourWayDataHandler 处理获取隧道四向数据
func (c *WebSocketService) tunnelFourWayDataHandler(pathComm dto.PathComm) {
	ticker := time.NewTicker(1 * time.Second)
	// 基准值设定
	baseValue := 10.0
	maxFluctuation := 2.0 // 最大波动范围±2.0

	for {
		// 检查是否还有订阅者
		// 使用锁保护对 ServiceSessionsMap 的读取
		repo.ServiceSessionsMapLock.Lock()
		sessionExists := c.wsManager.ServiceSessionsMap[fileds.TunnelFourWayData] != nil &&
			c.wsManager.ServiceSessionsMap[fileds.TunnelFourWayData][fileds.TunnelFourWayData] != nil
		repo.ServiceSessionsMapLock.Unlock()

		if !sessionExists {
			return
		}

		// 生成随机波动值
		tunnelFourWayData := dto.TunnelFourWayData{
			Top:    baseValue + (rand.Float64()*2-1)*maxFluctuation, // 8-12之间波动
			Bottom: baseValue + (rand.Float64()*2-1)*maxFluctuation, // 8-12之间波动
			Left:   baseValue + (rand.Float64()*2-1)*maxFluctuation, // 8-12之间波动
			Right:  baseValue + (rand.Float64()*2-1)*maxFluctuation, // 8-12之间波动
		}

		data := make(map[string]interface{})
		data[fileds.TunnelFourWayData] = tunnelFourWayData
		encodedData, err := json.Marshal(data)
		if err != nil {
			c.logger.Errorf("tunnelFourWayDataHandler: marshal tunnel four way data failed: %v", err)
			continue
		}

		c.wsManager.BroadCastData(encodedData, fileds.TunnelFourWayData, fileds.TunnelFourWayData, "")
		<-ticker.C
	}
}
