package cloudinfra

import (
	"encoding/json"
	"fmt"
	"time"
	"wukong-api/cloud_sdk/mqtt"
	"wukong-api/internal/fileds"
	"wukong-api/internal/repo"
)

const (
	DEFAULT_RETRY_COUNT   = 2
	DEFAULT_QOS           = 0
	QOS_2                 = 2
	DEFAULT_RETRY_TIMEOUT = 30 * time.Second // s
)

// 发布消息
func Publish(topic string, request interface{}, qos int) error {
	if checkIsOccupiedPermissions() {
		return nil
	}

	payload, err := json.Marshal(request)
	payloadStr := string(payload)
	if err != nil {
		repo.GetLogger().Errorf("publish topic: %s, payload: %s, error: %v", topic, payloadStr, err)
		return err
	}

	err = repo.MqttAgent.MqttPub(topic, payloadStr, qos)
	if err != nil {
		repo.GetLogger().Errorf("publish topic: %s, payload: %s, error: %v", topic, payloadStr, err)
		return err
	}

	repo.GetLogger().Debugf("publish topic: %s, payload: %s", topic, payloadStr)
	return nil
}

// 发布消息并等待带输出的回复
func PublishWithReplyOutput[U any](topic string, request any, tid string, bid string, qos int, retryCount int, timeout time.Duration) (*mqtt.CommonTopicResponse[mqtt.MqttReplyWithOutput[U]], error) {
	var lastErr error

	for i := 0; i < retryCount; i++ {
		if err := Publish(topic, request, qos); err != nil {
			lastErr = err
			continue
		}

		instance, err := GetInstance[mqtt.CommonTopicResponse[mqtt.MqttReplyWithOutput[U]]](tid, true)
		if err != nil {
			lastErr = err
			continue
		}

		receiver, err := instance.Get(tid, timeout)
		if err != nil {
			lastErr = err
			continue
		}

		if receiver != nil && receiver.Tid == tid && receiver.Bid == bid {
			return receiver, nil
		}
	}

	instance, _ := GetInstance[mqtt.CommonTopicResponse[mqtt.MqttReplyWithOutput[U]]](tid, true)
	if instance != nil {
		instance.Del(tid)
		repo.GetLogger().Debugf("del instance tid :%s", tid)
	}

	return nil, fmt.Errorf("failed to receive valid response after %d retries, last error: %v", retryCount, lastErr)
}

// 发布消息并等待普通回复
func PublishWithReply(topic string, request any, tid string, bid string, qos int, retryCount int, timeout time.Duration) (*mqtt.CommonTopicResponse[mqtt.MqttReply], error) {

	var lastErr error

	for i := 0; i < retryCount; i++ {
		if err := Publish(topic, request, qos); err != nil {
			lastErr = err
			continue
		}
		repo.GetLogger().Debugf("Publish: topic: %s, request: %v, qos: %d", topic, request, qos)

		instance, err := GetInstance[mqtt.CommonTopicResponse[mqtt.MqttReply]](tid, true)
		if err != nil {
			lastErr = err
			continue
		}

		repo.GetLogger().Debugf("GetInstance: tid: %s, instance: %v", tid, instance)

		receiver, err := instance.Get(tid, timeout)
		if err != nil {
			lastErr = err
			continue
		}

		repo.GetLogger().Debugf("Get: tid: %s, receiver: %v", tid, receiver)

		if receiver != nil && receiver.Tid == tid && receiver.Bid == bid {
			return receiver, nil
		}
	}

	instance, _ := GetInstance[mqtt.CommonTopicResponse[mqtt.MqttReply]](tid, true)
	if instance != nil {
		instance.Del(tid)
		repo.GetLogger().Debugf("del instance tid :%s", tid)
	}

	return nil, fmt.Errorf("failed to receive valid response after %d retries, last error: %v", retryCount, lastErr)
}

func PublishReply(topic string, response interface{}) error {
	return Publish(topic, response, QOS_2)
}

func PublishDrc(topic string, request interface{}, qos int) error {
	if checkIsOccupiedPermissions() {
		return nil
	}

	payload, err := json.Marshal(request)
	payloadStr := string(payload)
	if err != nil {
		repo.GetLogger().Errorf("publish topic: %s, payload: %s, error: %v", topic, payloadStr, err)
		return err
	}

	repo.DrcMqttAgent.MqttPub(topic, payloadStr, qos)
	repo.GetLogger().Debugf("publish topic: %s, payload: %s", topic, payloadStr)

	return nil
}

// PublishDrcWithReply
// fixme 先忽略reply，返回nil
func PublishDrcWithReply(topic string, request any, seq int, qos int, retryCount int, timeout time.Duration) (*mqtt.TopicDrcResponse[mqtt.MqttReply], error) {
	if checkIsOccupiedPermissions() {
		return nil, nil
	}

	err := PublishDrc(topic, request, qos)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// checkIsOccupiedPermissions 检查是否被占用
func checkIsOccupiedPermissions() bool {
	res, _ := repo.GetRedis(0).Get(fileds.IsOccupiedPermissions).Result()
	return res == "1"
}
