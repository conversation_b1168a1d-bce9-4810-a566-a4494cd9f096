package repo

import (
	"errors"
	"fmt"
	"log"
	"math/rand"
	"os"
	"strconv"
	"sync"
	"time"
	"wukong-api/internal/config"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// DRC MQTT错误定义
var (
	ErrDrcMqttClientNotConnected = errors.New("DRC MQTT client is not connected")
	ErrDrcMqttPublishTimeout     = errors.New("DRC MQTT publish timeout")
	ErrDrcMqttPublishFailed      = errors.New("DRC MQTT publish failed")
	ErrDrcMqttClientNil          = errors.New("DRC MQTT client is nil")
)

var drcMessagePubHandler mqtt.MessageHandler = func(client mqtt.Client, msg mqtt.Message) {
	fmt.Printf("[drc mqtt] received message: %s from topic: %s\n", msg.Payload(), msg.Topic())
}

var drcConnectHandler mqtt.OnConnectHandler = func(client mqtt.Client) {
	fmt.Println("[drc mqtt] connected")
}

var drcConnectLostHandler mqtt.ConnectionLostHandler = func(client mqtt.Client, err error) {
	fmt.Printf("[drc mqtt] connect lost: %v", err)
	os.Exit(1)
}

var drcReconnecting mqtt.ReconnectHandler = func(client mqtt.Client, options *mqtt.ClientOptions) {
	fmt.Printf("[drc mqtt] reconnecting...")
	os.Exit(1)
}

var drcMqttClient mqtt.Client
var drcMqttOne sync.Once

func getDrcClientId() string {
	cnf := config.GetConfig()
	podName := os.Getenv("MY_POD_NAME")
	if podName == "" {
		podName = cnf.Mqtt.Drc.ClientId + "_" + strconv.Itoa(rand.Int())
	}

	return podName
}

func initDrcMqtt() {
	rand.Seed(time.Now().UnixNano())
	drcMqttOne.Do(func() {
		cnf := config.GetConfig()

		opts := mqtt.NewClientOptions()
		opts.AddBroker(fmt.Sprintf("tcp://%s:%d", cnf.Mqtt.Drc.Broker, cnf.Mqtt.Drc.Port))
		opts.SetClientID(getDrcClientId())
		opts.SetUsername(cnf.Mqtt.Drc.User)
		opts.SetPassword(cnf.Mqtt.Drc.Password)
		opts.SetDefaultPublishHandler(drcMessagePubHandler)
		opts.SetOnConnectHandler(drcConnectHandler)
		opts.SetConnectionLostHandler(drcConnectLostHandler)
		opts.SetReconnectingHandler(drcReconnecting)
		opts.SetAutoReconnect(true)
		opts.SetConnectRetry(true)
		opts.SetConnectRetryInterval(10 * time.Second)
		opts.SetKeepAlive(3 * 60)
		opts.SetMaxReconnectInterval(10 * time.Second)
		opts.SetConnectTimeout(3 * time.Minute)

		drcMqttClient = mqtt.NewClient(opts)

		client := drcMqttClient
		if token := client.Connect(); token.Wait() && token.Error() != nil {
			log.Println("token error", token.Error())
		}
	})
}

type drcMqttAgent int

func (agent drcMqttAgent) MqttSub(topic string, msgCallback mqtt.MessageHandler) {
	token := agent.GetMqttClient().Subscribe(topic, 0, msgCallback)
	token.Wait()
}

// MqttPub 发布DRC MQTT消息，带有完整的错误处理和重试机制
func (agent drcMqttAgent) MqttPub(topic string, content string, qos int) error {
	return agent.MqttPubWithTimeout(topic, content, qos, DefaultPublishTimeout)
}

// MqttPubWithTimeout 发布DRC MQTT消息，支持自定义超时时间
func (agent drcMqttAgent) MqttPubWithTimeout(topic string, content string, qos int, timeout time.Duration) error {
	return agent.MqttPubWithRetry(topic, content, qos, timeout, DefaultRetryCount)
}

// MqttPubWithRetry 发布DRC MQTT消息，支持自定义超时时间和重试次数
func (agent drcMqttAgent) MqttPubWithRetry(topic string, content string, qos int, timeout time.Duration, retryCount int) error {
	logger := GetLogger()

	// 参数验证
	if topic == "" {
		err := errors.New("topic cannot be empty")
		logger.Errorf("[drc mqtt] MqttPub failed: %v", err)
		return err
	}

	if retryCount <= 0 {
		retryCount = 1
	}

	var lastErr error
	retryInterval := DefaultRetryInterval

	for attempt := 1; attempt <= retryCount; attempt++ {
		// 获取DRC MQTT客户端
		client := agent.GetMqttClient()
		if client == nil {
			lastErr = ErrDrcMqttClientNil
			logger.Errorf("[drc mqtt] MqttPub attempt %d/%d failed: %v, topic: %s", attempt, retryCount, lastErr, topic)

			if attempt < retryCount {
				time.Sleep(retryInterval)
				retryInterval = minDuration(retryInterval*2, MaxRetryInterval)
				continue
			}
			return lastErr
		}

		// 检查连接状态
		if !client.IsConnected() {
			lastErr = ErrDrcMqttClientNotConnected
			logger.Warnf("[drc mqtt] MqttPub attempt %d/%d: client not connected, topic: %s", attempt, retryCount, topic)

			// 尝试重新连接
			if token := client.Connect(); token.Wait() && token.Error() != nil {
				lastErr = fmt.Errorf("drc reconnect failed: %w", token.Error())
				logger.Errorf("[drc mqtt] MqttPub attempt %d/%d reconnect failed: %v, topic: %s", attempt, retryCount, lastErr, topic)

				if attempt < retryCount {
					time.Sleep(retryInterval)
					retryInterval = minDuration(retryInterval*2, MaxRetryInterval)
					continue
				}
				return lastErr
			}
			logger.Infof("[drc mqtt] MqttPub attempt %d/%d: reconnected successfully, topic: %s", attempt, retryCount, topic)
		}

		// 发布消息
		token := client.Publish(topic, byte(qos), false, content)

		// 等待发布完成，带超时控制
		if !token.WaitTimeout(timeout) {
			lastErr = ErrDrcMqttPublishTimeout
			logger.Errorf("[drc mqtt] MqttPub attempt %d/%d timeout after %v: topic: %s, content_length: %d",
				attempt, retryCount, timeout, topic, len(content))

			if attempt < retryCount {
				time.Sleep(retryInterval)
				retryInterval = minDuration(retryInterval*2, MaxRetryInterval)
				continue
			}
			return lastErr
		}

		// 检查发布结果
		if token.Error() != nil {
			lastErr = fmt.Errorf("%w: %v", ErrDrcMqttPublishFailed, token.Error())
			logger.Errorf("[drc mqtt] MqttPub attempt %d/%d failed: %v, topic: %s, qos: %d, content_length: %d",
				attempt, retryCount, lastErr, topic, qos, len(content))

			if attempt < retryCount {
				time.Sleep(retryInterval)
				retryInterval = minDuration(retryInterval*2, MaxRetryInterval)
				continue
			}
			return lastErr
		}

		// 发布成功
		logger.Debugf("[drc mqtt] MqttPub success on attempt %d/%d: topic: %s, qos: %d, content_length: %d",
			attempt, retryCount, topic, qos, len(content))
		return nil
	}

	// 所有重试都失败了
	logger.Errorf("[drc mqtt] MqttPub failed after %d attempts: %v, topic: %s", retryCount, lastErr, topic)
	return lastErr
}

func (agent drcMqttAgent) GetMqttClient() mqtt.Client {
	initDrcMqtt()
	return drcMqttClient
}

const DrcMqttAgent = drcMqttAgent(1)
