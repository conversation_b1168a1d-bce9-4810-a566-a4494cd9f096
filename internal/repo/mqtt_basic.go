package repo

import (
	"fmt"
	"log"
	"math/rand"
	"os"
	"strconv"
	"sync"
	"time"
	"wukong-api/internal/config"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

var messagePubHandler mqtt.MessageHandler = func(client mqtt.Client, msg mqtt.Message) {
	fmt.Printf("[mqtt] received message: %s from topic: %s\n", msg.Payload(), msg.Topic())
}

var connectHandler mqtt.OnConnectHandler = func(client mqtt.Client) {
	fmt.Println("[mqtt] connected")
}

var connectLostHandler mqtt.ConnectionLostHandler = func(client mqtt.Client, err error) {
	fmt.Printf("[mqtt] connect lost: %v", err)
	os.Exit(1)
}

var reconnecting mqtt.ReconnectHandler = func(client mqtt.Client, options *mqtt.ClientOptions) {
	fmt.Printf("[mqtt] reconnecting...")
	os.Exit(1)
}

var mqttClient mqtt.Client
var mqttOne sync.Once

func getClientId() string {
	cnf := config.GetConfig()
	podName := os.Getenv("MY_POD_NAME")
	if podName == "" {
		podName = cnf.Mqtt.Basic.ClientId + "_" + strconv.Itoa(rand.Int())
	}

	return podName
}

func initMqtt() {
	rand.Seed(time.Now().UnixNano())
	mqttOne.Do(func() {
		cnf := config.GetConfig()

		opts := mqtt.NewClientOptions()
		opts.AddBroker(fmt.Sprintf("tcp://%s:%d", cnf.Mqtt.Basic.Broker, cnf.Mqtt.Basic.Port))
		opts.SetClientID(getClientId())
		opts.SetUsername(cnf.Mqtt.Basic.User)
		opts.SetPassword(cnf.Mqtt.Basic.Password)
		opts.SetDefaultPublishHandler(messagePubHandler)
		opts.SetOnConnectHandler(connectHandler)
		opts.SetConnectionLostHandler(connectLostHandler)
		opts.SetReconnectingHandler(reconnecting)
		opts.SetAutoReconnect(true)
		opts.SetConnectRetry(true)
		opts.SetConnectRetryInterval(10 * time.Second)
		opts.SetKeepAlive(3 * 60)
		opts.SetMaxReconnectInterval(10 * time.Second)
		opts.SetConnectTimeout(3 * time.Minute)

		mqttClient = mqtt.NewClient(opts)

		client := mqttClient
		if token := client.Connect(); token.Wait() && token.Error() != nil {
			log.Println("token error", token.Error())
		}
	})
}

type mqttAgent int

func (agent mqttAgent) MqttSub(topic string, msgCallback mqtt.MessageHandler) {
	token := agent.GetMqttClient().Subscribe(topic, 0, msgCallback)
	token.Wait()
}

func (agent mqttAgent) MqttPub(topic string, content string, qos int) {
	token := agent.GetMqttClient().Publish(topic, byte(qos), false, content)
	token.Wait()
}

func (agent mqttAgent) GetMqttClient() mqtt.Client {
	initMqtt()
	return mqttClient
}

const MqttAgent = mqttAgent(1)
