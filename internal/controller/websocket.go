package controller

import (
	"wukong-api/internal/dto"
	"wukong-api/internal/fileds"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/qxsugar/pkg/apix"

	"net/http"
	"time"
	"wukong-api/internal/repo"
	"wukong-api/internal/service"
	"wukong-api/pkg/token"

	"go.uber.org/zap"
)

type WebSocketInterface interface {
	StartWebSocket(ctx *gin.Context) (interface{}, error)
	SubWebSocketService(ctx *gin.Context) (interface{}, error)
	UnSubWebSocketService(ctx *gin.Context) (interface{}, error)
	UpdateObjectKeyManager(ctx *gin.Context) (interface{}, error)
}

type WebSocketApi struct {
	logger    *zap.SugaredLogger
	wsManager *repo.WsManager
	service   *service.WebSocketService
}

// SubWebSocketService 订阅业务功能
func (c *WebSocketApi) SubWebSocketService(ctx *gin.Context) (interface{}, error) {
	var body dto.SubServiceReq
	err := ctx.ShouldBindJSON(&body)
	if err != nil {
		return nil, apix.NewInvalidArgumentError().WithErr(err)
	}

	// 获取uid
	//value, _ := ctx.Get(token.Userid)
	//i := value.(int)
	//uid := strconv.Itoa(i)

	key := body.ServiceKey

	// 校验用户id是否有注册websocket连接
	flag := false
	for k, _ := range c.wsManager.WsConn {
		if body.Id == k {
			flag = true
		}
	}
	if flag == false {
		return nil, apix.NewInvalidArgumentError().WithMsg("非法订阅:" + body.Id)
	}

	//校验service_key是否有效
	for _, item := range repo.ServiceKey {
		if item == key {
			flag = true
		}
	}
	if flag == false {
		return nil, apix.NewInvalidArgumentError().WithMsg("未知的service_key:" + key)
	}

	var extInfo dto.WSExtInfo
	if key == fileds.DroneMgrInfo || key == fileds.SandboxDeviceOverview || key == fileds.SandboxTrajectoryOverview || key == fileds.SandboxWarnRecordLatest {
		extInfo.CID = ctx.GetInt(token.Cid)
	}

	err = c.service.DispatchSub(body.Id, key, body.Params, extInfo)
	if err != nil {
		return nil, apix.NewInternalError().WithMsg("订阅业务功能错误")
	}

	return "订阅成功", nil
}

// UnSubWebSocketService 退订业务功能
func (c *WebSocketApi) UnSubWebSocketService(ctx *gin.Context) (interface{}, error) {
	serviceKey := ctx.Query("service_key")
	reqId := ctx.Query("id")

	if reqId == "" || serviceKey == "" {
		return nil, apix.NewInvalidArgumentError().WithMsg("缺少参数")
	}

	// 获取uid
	//value, _ := ctx.Get(token.Userid)
	//i := value.(int)
	//uid := strconv.Itoa(i)

	//  如果是 objectSignUrl，则根据 id.bid（path）清空对应私有资源的全局状态
	if serviceKey == fileds.ObjectSignUrl {
		path := ctx.Query("path")
		if path == "" {
			return nil, apix.NewInvalidArgumentError().WithMsg("缺少参数")
		}

		repo.OssPrivateObjManagerLock.Lock()
		if _, ok := c.wsManager.OssPrivateObjManager[reqId]; ok {
			if _, ok = c.wsManager.OssPrivateObjManager[reqId][path]; ok {
				delete(c.wsManager.OssPrivateObjManager[reqId], path)
			}
		}
		repo.OssPrivateObjManagerLock.Unlock()
	}

	repo.ServiceSessionsMapLock.Lock()
	defer repo.ServiceSessionsMapLock.Unlock()

	if serviceMap, ok := c.wsManager.ServiceSessionsMap[serviceKey]; ok {
		for bid, wsConMap := range serviceMap {
			for id, _ := range wsConMap {
				if id == reqId {
					delete(c.wsManager.ServiceSessionsMap[serviceKey][bid], id)
					// 如果bidMap为空了，就直接删掉bidMap
					if len(c.wsManager.ServiceSessionsMap[serviceKey][bid]) == 0 {
						delete(c.wsManager.ServiceSessionsMap[serviceKey], bid)
					}

					return serviceKey + "退订成功", nil
				}
			}
		}
	}

	return "退订失败", nil
}

func (c *WebSocketApi) StartWebSocket(ctx *gin.Context) (interface{}, error) {
	/*	// TODO token验证
		tk := ctx.GetHeader(token.AuthorizationHeaderWebsocket)
		fmt.Println(tk)
		if tk == "" {
			return nil, apix.NewUnauthenticatedError().WithMsg("can't find authorization token")
		}
		_, err := token.Parse(tk)
		if err != nil {
			return nil, apix.NewUnauthenticatedError().WithMsg("token is invalid or expired")
		}*/
	id := ctx.Query("id")
	if id == "" {
		return nil, apix.NewInvalidArgumentError().WithMsg("缺少参数")
	}

	conn, err := (&websocket.Upgrader{CheckOrigin: func(r *http.Request) bool { return true }}).Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		return nil, apix.NewInternalError().WithMsg("websocket连接异常")
	}

	client := &repo.WsConn{
		Id:     id,
		Socket: conn,
		Send:   make(chan []byte),
	}

	// 注册当前websocket连接会话
	c.wsManager.Register <- client

	go client.WsWrite()
	go client.WsRead(c.service.DispatchReceivedData)

	return nil, nil
}

// UpdateObjectKeyManager 支持在已有的objectSignUrl订阅上新增删除 objectKey
func (c *WebSocketApi) UpdateObjectKeyManager(ctx *gin.Context) (interface{}, error) {
	req := struct {
		Add    []string `json:"add"`
		Delete []string `json:"delete"`
		Id     string   `json:"id" binding:"required"`
		Path   string   `json:"path" binding:"required"`
	}{}

	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		return nil, apix.NewInvalidArgumentError()
	}

	repo.ServiceSessionsMapLock.Lock()
	defer repo.ServiceSessionsMapLock.Unlock()

	// 判断是否有订阅未订阅objectSignUrl
	if _, ok := c.wsManager.ServiceSessionsMap[fileds.ObjectSignUrl]; !ok {
		return nil, apix.NewInvalidArgumentError().WithMsg("未订阅objectSignUrl")
	}
	if _, ok := c.wsManager.ServiceSessionsMap[fileds.ObjectSignUrl][req.Path]; !ok {
		return nil, apix.NewInvalidArgumentError().WithMsg("未订阅objectSignUrl")
	}
	if _, ok := c.wsManager.ServiceSessionsMap[fileds.ObjectSignUrl][req.Path][req.Id]; !ok {
		return nil, apix.NewInvalidArgumentError().WithMsg("未订阅objectSignUrl")
	}

	// 删除
	if req.Delete != nil && len(req.Delete) > 0 {
		for _, key := range req.Delete {
			repo.OssPrivateObjManagerLock.Lock()
			delete(c.wsManager.OssPrivateObjManager[req.Id][req.Path], key)
			repo.OssPrivateObjManagerLock.Unlock()
		}
	}

	// 新增
	if req.Add != nil && len(req.Add) > 0 {
		for _, key := range req.Add {
			repo.OssPrivateObjManagerLock.Lock()
			c.wsManager.OssPrivateObjManager[req.Id][req.Path][key] = time.Now().Unix()
			repo.OssPrivateObjManagerLock.Unlock()
		}

		// 手动执行检查，为了及时推送新的objectKey对应的签名地址
		err = c.service.CheckObjectKeyExpire(req.Id, req.Path)
		if err != nil {
			return nil, apix.NewInternalError().WithErr(err)
		}
	}

	return "success", nil
}

var _ WebSocketInterface = (*WebSocketApi)(nil)

func NewWebSocketApi() WebSocketInterface {
	return &WebSocketApi{
		logger:    repo.GetLogger(),
		wsManager: repo.NewWsManager(),
		service:   service.NewWebSocketService(),
	}
}
